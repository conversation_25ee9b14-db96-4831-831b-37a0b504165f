import { Transaction } from "@mysten/sui/transactions";
import { getFullnodeUrl, SuiClient } from "@mysten/sui/client";
import { Ed25519Keypair } from "@mysten/sui/keypairs/ed25519";
import { decodeSuiPrivateKey } from "@mysten/sui/cryptography";
import { CachingSuiClient } from "./cachingSuiClient.js";
import { SuiHTTPTransport } from "./sui_transport.js";
import dotenv from "dotenv";
import { CachingSuiTransactionExecutor } from "./cacheTx.js";
import axios from "axios";
const config = {
  moonbagConfig: {
    platformAdminAddress:
      "0x0d66d7ac45c1d011fdd21c5d4d193913c8c40d5f1e1e62e33f72ffa0f2b86e2a",
    treasuryAddress:
      "0x240620c61a515e15459a0cfa95bcf88527382bc87aef2c2491acff715619799b",
    initVirtualTokenReserves: "1066666666666600",
    remainTokenReserves: "266666666666600",
    platformTradingFeeRate: 0.01,
    minimumTargetRaise: "2000000000000",
    cetusFeeRate: 0.01,
    platformDeploymentFee: "100000000",
    lockFeeRate: 0,
  },
  suiObjectIds: {
    moonbagPackage:
      "0x1f2fd9f03575a5dd8a0482ea9a32522fa5f4ec8073a14a5362efd3833d415a7e",
    contractConfig:
      "0x74aecf86067c6913960ba4925333aefd2b1f929cafca7e21fd55a8f244b70499",
    stakeConfig:
      "0x245161e22ea04614628b56da68fe0474fff8c3c631292c2ee1a0bd669db57959",
    lockConfig:
      "0xfb09822d9808980abd04c51321adb850701f5f55535c6206658ef4d910c3e9be",
    cetusGlobalConfig:
      "0xdaa46292632c3c4d8f31f23ea0f9b36a28ff3677e9684980e4438403a67a3d8f",
    cetusFactoryPool:
      "0xf699e7f2276f5c9a75944b37a0c5b5d9ddfd2471bf6242483b03ab2887d198d0",
    cetusPoolType:
      "0x1eabed72c53feb3805120a081dc15963c204dc8d091542592abaf7a35689b2fb::pool::Pool",
    suiMetadata:
      "0x9258181f5ceac8dbffb7030890243caed69a9599d2886d957a9cb7656af3bdb3",
    shroType:
      "0x16ab6a14d76a90328a6b04f06b0a0ce952847017023624e0c37bf8aa314c39ba::shr::SHR",
    cetusBurnManager:
      "0x1d94aa32518d0cb00f9de6ed60d450c9a2090761f326752ffad06b2e9404f845",
  },
};
const REQUEST_TIMEOUT_DEFAULT = 2_000; // Optimized for speed
dotenv.config({});
const signer = Ed25519Keypair.fromSecretKey(
  decodeSuiPrivateKey(process.env.PRIVATE_KEY).secretKey
);
const suiClient = new CachingSuiClient({
  transport: new SuiHTTPTransport({
    rpcEndpoints: process.env.RPC_ENDPOINTS.split(","),
    timeout: REQUEST_TIMEOUT_DEFAULT,
    apiKey: process.env.SHINAMI_API_KEY,
  }),
});
const txExecutor = new CachingSuiTransactionExecutor({
  client: suiClient,
});

const url =
  // eslint-disable-next-line max-len
  "https://api2.moonbags.io/api/v1/staking/coins?includeByWalletAddress=0x7adcbc55d9c2ee9532bb478b371baf71aad458ff6df40ea74487ab41c6d8bd3e&limit=30";

const token =
  // eslint-disable-next-line max-len
  "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZGRyZXNzIjoiMHg3YWRjYmM1NWQ5YzJlZTk1MzJiYjQ3OGIzNzFiYWY3MWFhZDQ1OGZmNmRmNDBlYTc0NDg3YWI0MWM2ZDhiZDNlIiwiaWF0IjoxNzQ3ODM1MTk3LCJleHAiOjE3NDc5MjE1OTd9.LQLwwpWGXNYcDifyRp30GP6qP3cYvBkAvkVtg3z5Dsk";

let page = 1;
const realData = [];
const getCoinRewards = async (_page) => {
  try {
    const data = await axios.get(`${url}&page=${_page}`, {
      headers: {
        Authorization: token,
      },
    });
    realData.push(...data.data.docs);
    console.log(realData.length, "real data");
    if (data.data.hasNextPage) {
      page += _page;
      await getCoinRewards(page);
    }
  } catch (error) {
    console.log(error, "getCoinRewards error");
  }
};

async function handleUnstake(coinType, amount) {
  try {
    const { moonbagPackage, stakeConfig } = config.suiObjectIds;

    const tx = new Transaction();
    const clock = tx.object("0x6");
    const stakeConfigObject = tx.object(stakeConfig);

    tx.moveCall({
      target: `${moonbagPackage}::moonbags_stake::unstake`,
      typeArguments: [coinType],
      arguments: [
        stakeConfigObject,
        tx.pure.u64(convertDecToMist(amount, 6)),
        clock,
      ],
    });

    const result = await txExecutor.signAndExecuteTransaction({
      transaction: tx,
      signer: signer,
      options: {
        showEffects: true,
        showEvents: true,
        showObjectChanges: true,
      },
    });
    console.log("result", result?.balanceChanges);
  } catch (error) {
    console.log(error);
  }
}

async function handleSell(tokenAddress, sellAmount) {
  try {
    const { contractConfig, moonbagPackage } = config.suiObjectIds;

    const tx = new Transaction();
    tx.setSender(signer.toSuiAddress());
    const coin = coinWithBalance({
      type: tokenAddress,
      balance: BigInt(convertDecToMist(sellAmount, 6)),
    })(tx);

    tx.moveCall({
      target: `${moonbagPackage}::moonbags::sell`,
      typeArguments: [tokenAddress],
      arguments: [
        tx.object(contractConfig),
        coin,
        tx.pure.u64("0"),
        tx.object("0x6"),
      ],
    });
    const result = await txExecutor.signAndExecuteTransaction({
      transaction: tx,
      signer: this.signer,
      options: {
        showEffects: true,
        showEvents: true,
        showObjectChanges: true,
      },
    });
    console.log(
      { txid: result.digest, status: result.effects?.status },
      "sell result"
    );
  } catch (error) {
    console.log("Sell error", error);
  }
}

async function handleClaim(coinType) {
  try {
    const tx = new Transaction();
    const { stakeConfig, moonbagPackage } = config.suiObjectIds;
    const clock = tx.object("0x6");

    tx.moveCall({
      target: `${moonbagPackage}::moonbags_stake::claim_staking_pool`,
      typeArguments: [coinType],
      arguments: [tx.object(stakeConfig), clock],
    });

    tx.setGasBudget(10000000);

    const result = await txExecutor.signAndExecuteTransaction({
      transaction: tx,
      signer,
      options: {
        showEffects: true,
        showEvents: true,
        showObjectChanges: true,
      },
    });
    console.log(
      { txid: result.digest, status: result.effects?.status },
      "handleClaim result"
    );
  } catch (error) {
    console.log("handle claim error", error);
  }
}

async function claimRewardOnchain() {
  try {
    await getCoinRewards(page);
    const coinRewards = realData
      .filter((item) => {
        return +item.includeRewardAmount > 0.001;
      })
      .map((item) => {
        return {
          tokenAddress: item.tokenAddress,
          amount: item.includeRewardAmount,
        };
      });

    for (const coinAddress of coinRewards) {
      await handleClaim(coinAddress.tokenAddress);
    }
  } catch (error) {
    console.log("claim error", error);
  }
}

async function claimAndSell() {
  try {
    await getCoinRewards(page);
    const coinRewards = realData
      .filter((item) => {
        console.log(
          item.includeStakedAmount,
          item.tokenAddress,
          "item.includeStakedAmount"
        );
        return +item.includeStakedAmount > 10000;
      })
      .map((item) => {
        return {
          tokenAddress: item.tokenAddress,
          amount: item.includeStakedAmount,
        };
      });
    console.log(coinRewards, "coinRewards");
    for (const coinAddress of coinRewards) {
      await handleUnstake(coinAddress.tokenAddress, coinAddress.amount);
      await sleep(2000);

      await handleSell(coinAddress.tokenAddress, coinAddress.amount);
    }
  } catch (error) {
    console.log("claim error", error);
  }
}

//chay claim truoc
claimRewardOnchain();

// roi chay claimAndSell()
