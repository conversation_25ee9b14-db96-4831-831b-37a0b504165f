import { JsonRpcError } from "@mysten/sui/client";

export class SuiHTTPTransport {
  constructor(options) {
    const { rpcEndpoints, timeout, apiKey } = options;
    this.rpcEndpoints = Array.isArray(rpcEndpoints)
      ? rpcEndpoints
      : [rpcEndpoints];
    this.currentEndpointIndex = 0;
    this.timeout = timeout ?? 30000;
    this.apiKey = apiKey;
  }

  getCurrentEndpoint() {
    return this.rpcEndpoints[this.currentEndpointIndex];
  }

  switchToNextEndpoint() {
    this.currentEndpointIndex =
      (this.currentEndpointIndex + 1) % this.rpcEndpoints.length;
    return this.getCurrentEndpoint();
  }

  async fetch(url, reqInit, signal) {
    const response = await fetch(url, {
      ...reqInit,
      signal: signal ?? AbortSignal.timeout(this.timeout),
      keepalive: true,
    });
    return response;
  }

  async request(input, retryCount = 0) {
    // For critical operations, use parallel requests to all endpoints
    if (this.shouldUseParallelRequests(input.method)) {
      return this.parallelRequest(input);
    }

    // Fallback to sequential requests for non-critical operations
    return this.sequentialRequest(input, retryCount);
  }

  shouldUseParallelRequests(method) {
    // Use parallel requests for time-sensitive operations
    const criticalMethods = [
      "suix_queryEvents",
      "sui_executeTransactionBlock",
      "sui_dryRunTransactionBlock",
      "suix_getLatestSuiSystemState",
    ];
    return criticalMethods.includes(method);
  }

  async parallelRequest(input) {
    const requestBody = JSON.stringify({
      jsonrpc: "2.0",
      id: 1,
      method: input.method,
      params: input.params,
    });

    const getRequestOptions = (endpoint) => {
      const headers = {
        "Content-Type": "application/json",
      };

      // Add Shinami API key header if endpoint is Shinami and we have an API key
      if (this.apiKey && endpoint.includes('shinami.com')) {
        headers["X-Api-Key"] = this.apiKey;
      }

      return {
        method: "POST",
        headers,
        body: requestBody,
      };
    };

    // Create promises for all endpoints
    const promises = this.rpcEndpoints.map(async (endpoint, index) => {
      try {
        const res = await this.fetch(endpoint, getRequestOptions(endpoint), input.signal);

        if (!res.ok) {
          throw new Error(
            `Endpoint ${index}: Unexpected status code: ${res.status}`
          );
        }

        const data = await res.json();
        if ("error" in data && data.error != null) {
          throw new JsonRpcError(
            `Endpoint ${index}: ${data.error.message}`,
            data.error.code
          );
        }

        return { result: data.result, endpointIndex: index };
      } catch (error) {
        throw new Error(`Endpoint ${index}: ${error.message}`);
      }
    });

    // Race all requests and return the fastest successful response
    try {
      const { result, endpointIndex } = await Promise.race(promises);
      // Update current endpoint to the fastest one for future sequential requests
      this.currentEndpointIndex = endpointIndex;
      return result;
    } catch (error) {
      // If race fails, try to get any successful response
      const results = await Promise.allSettled(promises);
      const successful = results.find((r) => r.status === "fulfilled");

      if (successful) {
        return successful.value.result;
      }

      // If all failed, throw the first error
      throw new Error(
        `All endpoints failed: ${results
          .map((r) => r.reason?.message)
          .join(", ")}`
      );
    }
  }

  async sequentialRequest(input, retryCount = 0) {
    const maxRetries = this.rpcEndpoints.length;

    try {
      const currentEndpoint = this.getCurrentEndpoint();
      const headers = {
        "Content-Type": "application/json",
      };

      // Add Shinami API key header if endpoint is Shinami and we have an API key
      if (this.apiKey && currentEndpoint.includes('shinami.com')) {
        headers["X-Api-Key"] = this.apiKey;
      }

      const res = await this.fetch(
        currentEndpoint,
        {
          method: "POST",
          headers,
          body: JSON.stringify({
            jsonrpc: "2.0",
            id: 1,
            method: input.method,
            params: input.params,
          }),
        },
        input.signal
      );

      if (!res.ok) {
        throw new Error(`Unexpected status code: ${res.status}`);
      }

      const data = await res.json();
      if ("error" in data && data.error != null) {
        throw new JsonRpcError(`${data.error.message}`, data.error.code);
      }

      return data.result;
    } catch (error) {
      if (retryCount < maxRetries - 1) {
        // Switch to next endpoint and retry
        this.switchToNextEndpoint();
        return this.sequentialRequest(input, retryCount + 1);
      }
      throw error;
    }
  }

  async subscribe(input) {
    return () => Promise.resolve(true);
  }
}
