
import { bcs, fromHEX, toHEX } from '@mysten/bcs'

const Address = bcs.bytes(32).transform({
            input: val => fromHEX(val),
            output: val => toHEX(val),
          });
const ID = bcs.struct('ID', {
            bytes: Address
          });
const UID = bcs.struct('UID', {
            id: ID
          });
const String = bcs.struct('String', {
            bytes: bcs.vector(bcs.u8())
          });
const Balance = (T0) => bcs.struct(`Balance<${T0}>`, {
            value: bcs.u64()
          });
const Coin = (T0) => bcs.struct(`Coin<${T0}>`, {
            id: UID,
    balance: Balance(T0)
          });
const SUI = bcs.struct('SUI', {
            dummy_field: bcs.bool()
          });

export default {
  
  moonbags: {
    AdminCap: bcs.struct('AdminCap', {
            id: UID
          }),
    ConfigChangedEvent: bcs.struct('ConfigChangedEvent', {
            old_platform_fee: bcs.u64(),
    new_platform_fee: bcs.u64(),
    old_initial_virtual_token_reserves: bcs.u64(),
    new_initial_virtual_token_reserves: bcs.u64(),
    old_remain_token_reserves: bcs.u64(),
    new_remain_token_reserves: bcs.u64(),
    old_token_decimals: bcs.u8(),
    new_token_decimals: bcs.u8(),
    old_init_platform_fee_withdraw: bcs.u16(),
    new_init_platform_fee_withdraw: bcs.u16(),
    old_init_creator_fee_withdraw: bcs.u16(),
    new_init_creator_fee_withdraw: bcs.u16(),
    old_init_stake_fee_withdraw: bcs.u16(),
    new_init_stake_fee_withdraw: bcs.u16(),
    old_init_platform_stake_fee_withdraw: bcs.u16(),
    new_init_platform_stake_fee_withdraw: bcs.u16(),
    old_token_platform_type_name: String,
    new_token_platform_type_name: String,
    ts: bcs.u64()
          }),
    Configuration: bcs.struct('Configuration', {
            id: UID,
    version: bcs.u64(),
    admin: Address,
    treasury: Address,
    fee_platform_recipient: Address,
    platform_fee: bcs.u64(),
    initial_virtual_token_reserves: bcs.u64(),
    remain_token_reserves: bcs.u64(),
    token_decimals: bcs.u8(),
    init_platform_fee_withdraw: bcs.u16(),
    init_creator_fee_withdraw: bcs.u16(),
    init_stake_fee_withdraw: bcs.u16(),
    init_platform_stake_fee_withdraw: bcs.u16(),
    token_platform_type_name: String
          }),
    CreatedEvent: bcs.struct('CreatedEvent', {
            name: String,
    symbol: String,
    uri: String,
    description: String,
    twitter: String,
    telegram: String,
    website: String,
    token_address: String,
    bonding_curve: String,
    pool_id: ID,
    created_by: Address,
    virtual_sui_reserves: bcs.u64(),
    virtual_token_reserves: bcs.u64(),
    real_sui_reserves: bcs.u64(),
    real_token_reserves: bcs.u64(),
    platform_fee_withdraw: bcs.u16(),
    creator_fee_withdraw: bcs.u16(),
    stake_fee_withdraw: bcs.u16(),
    platform_stake_fee_withdraw: bcs.u16(),
    threshold: bcs.u64(),
    ts: bcs.u64()
          }),
    OwnershipTransferredEvent: bcs.struct('OwnershipTransferredEvent', {
            old_admin: Address,
    new_admin: Address,
    ts: bcs.u64()
          }),
    Pool: (T0) => bcs.struct(`Pool<${T0}>`, {
            id: UID,
    real_sui_reserves: Coin(SUI),
    real_token_reserves: Coin(T0),
    virtual_token_reserves: bcs.u64(),
    virtual_sui_reserves: bcs.u64(),
    remain_token_reserves: Coin(T0),
    fee_recipient: Coin(SUI),
    is_completed: bcs.bool(),
    platform_fee_withdraw: bcs.u16(),
    creator_fee_withdraw: bcs.u16(),
    stake_fee_withdraw: bcs.u16(),
    platform_stake_fee_withdraw: bcs.u16(),
    threshold: bcs.u64()
          }),
    PoolCompletedEvent: bcs.struct('PoolCompletedEvent', {
            token_address: String,
    lp: String,
    ts: bcs.u64()
          }),
    ThresholdConfig: bcs.struct('ThresholdConfig', {
            id: UID,
    threshold: bcs.u64()
          }),
    TradedEvent: bcs.struct('TradedEvent', {
            is_buy: bcs.bool(),
    user: Address,
    token_address: String,
    sui_amount: bcs.u64(),
    token_amount: bcs.u64(),
    virtual_sui_reserves: bcs.u64(),
    virtual_token_reserves: bcs.u64(),
    real_sui_reserves: bcs.u64(),
    real_token_reserves: bcs.u64(),
    pool_id: ID,
    fee: bcs.u64(),
    ts: bcs.u64()
          })
  },
  
  moonbags_stake: {
    AdminCap: bcs.struct('AdminCap', {
            id: UID
          }),
    ClaimCreatorPoolEvent: bcs.struct('ClaimCreatorPoolEvent', {
            token_address: String,
    creator_pool: ID,
    claimer: String,
    reward: bcs.u64(),
    timestamp: bcs.u64()
          }),
    ClaimStakingPoolEvent: bcs.struct('ClaimStakingPoolEvent', {
            token_address: String,
    staking_pool: ID,
    staking_account: ID,
    is_staking_account_deleted: bcs.bool(),
    claimer: String,
    reward: bcs.u64(),
    timestamp: bcs.u64()
          }),
    Configuration: bcs.struct('Configuration', {
            id: UID,
    version: bcs.u64(),
    admin: Address,
    deny_unstake_duration_ms: bcs.u64()
          }),
    CreatorPool: (T0) => bcs.struct(`CreatorPool<${T0}>`, {
            id: UID,
    sui_token: Coin(SUI),
    creator: Address
          }),
    DepositPoolCreatorEvent: bcs.struct('DepositPoolCreatorEvent', {
            token_address: String,
    creator_pool: ID,
    depositor: String,
    amount: bcs.u64(),
    timestamp: bcs.u64()
          }),
    InitializeCreatorPoolEvent: bcs.struct('InitializeCreatorPoolEvent', {
            token_address: String,
    creator_pool: ID,
    initializer: String,
    creator: String,
    timestamp: bcs.u64()
          }),
    InitializeStakingPoolEvent: bcs.struct('InitializeStakingPoolEvent', {
            token_address: String,
    staking_pool: ID,
    initializer: String,
    timestamp: bcs.u64()
          }),
    StakeEvent: bcs.struct('StakeEvent', {
            token_address: String,
    staking_pool: ID,
    staking_account: ID,
    staker: String,
    amount: bcs.u64(),
    timestamp: bcs.u64()
          }),
    StakingAccount: bcs.struct('StakingAccount', {
            id: UID,
    staker: Address,
    balance: bcs.u64(),
    reward_index: bcs.u128(),
    earned: bcs.u64(),
    unstake_deadline: bcs.u64()
          }),
    StakingPool: (T0) => bcs.struct(`StakingPool<${T0}>`, {
            id: UID,
    staking_token: Coin(T0),
    sui_token: Coin(SUI),
    total_supply: bcs.u64(),
    reward_index: bcs.u128(),
    pending_initial_rewards: bcs.u64()
          }),
    UnstakeEvent: bcs.struct('UnstakeEvent', {
            token_address: String,
    staking_pool: ID,
    staking_account: ID,
    is_staking_account_deleted: bcs.bool(),
    unstaker: String,
    amount: bcs.u64(),
    timestamp: bcs.u64()
          }),
    UpdateRewardIndexEvent: bcs.struct('UpdateRewardIndexEvent', {
            token_address: String,
    staking_pool: ID,
    reward_updater: String,
    reward: bcs.u64(),
    timestamp: bcs.u64(),
    is_initial_rewards: bcs.bool()
          })
  },
  
  moonbags_token_lock: {
    AdminCap: bcs.struct('AdminCap', {
            id: UID
          }),
    Configuration: bcs.struct('Configuration', {
            id: UID,
    lock_fee: bcs.u64(),
    admin: Address
          }),
    LockContract: (T0) => bcs.struct(`LockContract<${T0}>`, {
            id: UID,
    balance: Balance(T0),
    amount: bcs.u64(),
    start_time: bcs.u64(),
    end_time: bcs.u64(),
    locker: Address,
    recipient: Address,
    closed: bcs.bool()
          }),
    LockCreatedEvent: bcs.struct('LockCreatedEvent', {
            contract_id: Address,
    token_address: String,
    locker: Address,
    recipient: Address,
    amount: bcs.u64(),
    fee: bcs.u64(),
    start_time: bcs.u64(),
    end_time: bcs.u64()
          }),
    TokensWithdrawnEvent: bcs.struct('TokensWithdrawnEvent', {
            contract_id: Address,
    sender: Address,
    recipient: Address,
    amount: bcs.u64()
          })
  },
  
  utils: {
    DU256: bcs.struct('DU256', {
            v0: bcs.u64(),
    v1: bcs.u64(),
    v2: bcs.u64(),
    v3: bcs.u64(),
    v4: bcs.u64(),
    v5: bcs.u64(),
    v6: bcs.u64(),
    v7: bcs.u64()
          }),
    U256: bcs.struct('U256', {
            v0: bcs.u64(),
    v1: bcs.u64(),
    v2: bcs.u64(),
    v3: bcs.u64()
          })
  }
}
  