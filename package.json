{"name": "moonsnipe", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "postinstall": "patch-package"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aresrpg/sui-checkpoint-reader": "^4.2.7", "@cetusprotocol/sui-clmm-sdk": "^1.1.3", "@mysten/sui": "^1.29.1", "@shinami/clients": "^0.9.6", "axios": "^1.9.0", "bignumber.js": "^9.3.0", "dotenv": "^16.5.0", "dotnet": "^1.1.4", "lodash-es": "^4.17.21", "p-retry": "^3.0.1", "patch-package": "^8.0.0", "ws": "^8.18.2"}}