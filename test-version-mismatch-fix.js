import { CachingSuiTransactionExecutor } from "./cacheTx.js";
import { CachingSuiClient } from "./cachingSuiClient.js";
import { SuiHTTPTransport } from "./sui_transport.js";
import { Ed25519Keypair } from "@mysten/sui/keypairs/ed25519";
import { decodeSuiPrivateKey } from "@mysten/sui/cryptography";
import { Transaction } from "@mysten/sui/transactions";
import dotenv from "dotenv";

dotenv.config({});

// Test the version mismatch handling
async function testVersionMismatchHandling() {
  console.log("Testing version mismatch handling...");
  
  const signer = Ed25519Keypair.fromSecretKey(
    decodeSuiPrivateKey(process.env.PRIVATE_KEY).secretKey
  );
  
  const client = new CachingSuiClient({
    transport: new SuiHTTPTransport({
      rpcEndpoints: process.env.RPC_ENDPOINTS.split(","),
      timeout: 2000,
      apiKey: process.env.SHINAMI_API_KEY,
    }),
  });
  
  const txExecutor = new CachingSuiTransactionExecutor({
    client: client,
  });
  
  // Test the refreshForVersionMismatch method
  console.log("Testing refreshForVersionMismatch method...");
  try {
    await txExecutor.refreshForVersionMismatch();
    console.log("✅ refreshForVersionMismatch method works correctly");
  } catch (error) {
    console.error("❌ refreshForVersionMismatch method failed:", error.message);
  }
  
  // Test enhanced error detection
  console.log("Testing enhanced error detection...");
  
  const testErrors = [
    new Error("Object ID 0x123 with Version 0x456 is not available for consumption"),
    new Error("Transaction validator signing failed"),
    new Error("version mismatch detected"),
    new Error("Some other error")
  ];
  
  testErrors.forEach((error, index) => {
    const isVersionMismatch = error.isVersionMismatch || (
      error.message &&
      (error.message.includes("not available for consumption") ||
        error.message.includes("version mismatch") ||
        error.message.includes("Transaction validator signing failed"))
    );
    
    const expected = index < 3; // First 3 should be detected as version mismatches
    if (isVersionMismatch === expected) {
      console.log(`✅ Test ${index + 1}: Correctly detected version mismatch = ${isVersionMismatch}`);
    } else {
      console.log(`❌ Test ${index + 1}: Incorrectly detected version mismatch = ${isVersionMismatch}, expected = ${expected}`);
    }
  });
  
  console.log("Version mismatch handling test completed!");
}

// Run the test
testVersionMismatchHandling().catch(console.error);
