export const on_tick = (worker, options) => {
  const { interval } = options;

  worker
    .doProcess()
    .then(() => {
      setTimeout(() => {
        on_tick(worker, options);
      }, interval);
    })
    .catch((err) => {
      console.error(
        `The worker ${worker.name || ''} will be restarted shortly due to error:`,
        err
      );
      setTimeout(() => {
        on_tick(worker, options);
      }, interval);
    });
};
