import dotenv from "dotenv";
import moonbagBcs from "./moonbag-bcs.js";
import { coinWithBalance, Transaction } from "@mysten/sui/transactions";
import { getFullnodeUrl, SuiClient } from "@mysten/sui/client";
import { Ed25519Keypair } from "@mysten/sui/keypairs/ed25519";
import { decodeSuiPrivateKey } from "@mysten/sui/cryptography";
import { BigNumber } from "bignumber.js";
import { bcs } from "@mysten/bcs";
import {
  SUI_TYPE_ARG,
  SUI_DECIMALS,
  SUI_CLOCK_OBJECT_ID,
} from "@mysten/sui/utils";
// Removed withRetry import - no longer using retries for sniping

import { CachingSuiTransactionExecutor } from "./cacheTx.js";
import { CachingSuiClient } from "./cachingSuiClient.js";
import { SuiHTTPTransport } from "./sui_transport.js";
import { on_tick } from "./utils.js";
import axios from "axios";
import { CetusClmmSDK } from "@cetusprotocol/sui-clmm-sdk";

dotenv.config({});
const cetusSdk = CetusClmmSDK.createSDK({
  env: "mainnet",
});
const config = {
  moonbagConfig: {
    platformAdminAddress:
      "0x0b14e1e4b99ae114f4688c48758053c65575791159afdd8f2323781432ec79f1",
    treasuryAddress:
      "0x240620c61a515e15459a0cfa95bcf88527382bc87aef2c2491acff715619799b",
    initVirtualTokenReserves: "1066666666666600",
    remainTokenReserves: "266666666666600",
    platformTradingFeeRate: 0.01,
    minimumTargetRaise: "2000000000000",
    cetusFeeRate: 0.01,
    platformDeploymentFee: "100000000",
    lockFeeRate: 0,
  },
  suiObjectIds: {
    moonbagPackage:
      "0xa130ecff86d8f6ace3fcc1f390293c042b4283a68bc07df26fca02c00b05ff7e",
    contractConfig:
      "0x74aecf86067c6913960ba4925333aefd2b1f929cafca7e21fd55a8f244b70499",
    stakeConfig:
      "0x245161e22ea04614628b56da68fe0474fff8c3c631292c2ee1a0bd669db57959",
    lockConfig:
      "0xfb09822d9808980abd04c51321adb850701f5f55535c6206658ef4d910c3e9be",
    cetusGlobalConfig:
      "0xdaa46292632c3c4d8f31f23ea0f9b36a28ff3677e9684980e4438403a67a3d8f",
    cetusFactoryPool:
      "0xf699e7f2276f5c9a75944b37a0c5b5d9ddfd2471bf6242483b03ab2887d198d0",
    cetusPoolType:
      "0x1eabed72c53feb3805120a081dc15963c204dc8d091542592abaf7a35689b2fb::pool::Pool",
    suiMetadata:
      "0x9258181f5ceac8dbffb7030890243caed69a9599d2886d957a9cb7656af3bdb3",
    shroType:
      "0x16ab6a14d76a90328a6b04f06b0a0ce952847017023624e0c37bf8aa314c39ba::shr::SHR",
    cetusBurnManager:
      "0x1d94aa32518d0cb00f9de6ed60d450c9a2090761f326752ffad06b2e9404f845",
  },
};
const EVENT_TYPE = `0xa9aee0477f07c13ecca43d090bb0254af44986806bdfa92db24be4301b7b137f::moonbags::CreatedEventV2`;
const known_types = {
  [config.suiObjectIds.moonbagPackage]: moonbagBcs,
};

const SUI_DECIMAL_SCALAR = Math.pow(10, SUI_DECIMALS);
const GAS_BUDGET_DEFAULT = 0.51 * SUI_DECIMAL_SCALAR; // 0.31 SUI
const EPOCH_BOUNDARY_WINDOW = 1_000;
const GAS_PRICE_MULTIPLIER = 2.0; // Use 2x reference gas price for faster inclusion

const REQUEST_TIMEOUT_DEFAULT = 2_000; // Reduced from 10s to 2s for faster responses
const SCHEDULING_MS = 50;

const GLOBAL_STATE = {
  gasPrice: null,
  gasPriceRefreshing: false,
};
const GAS_PRICE = process.env.GAS_PRICE;
const coinsBuySuccess = [];

const suiClient = new CachingSuiClient({
  transport: new SuiHTTPTransport({
    rpcEndpoints: process.env.RPC_ENDPOINTS.split(","),
    timeout: REQUEST_TIMEOUT_DEFAULT,
    apiKey: process.env.SHINAMI_API_KEY,
  }),
});

async function refresh_gas_price() {
  if (GLOBAL_STATE.gasPriceRefreshing) {
    return; // Avoid concurrent refreshes
  }

  GLOBAL_STATE.gasPriceRefreshing = true;

  try {
    if (GLOBAL_STATE.gasPrice) {
      const timeToNextEpoch = Math.max(
        GLOBAL_STATE.gasPrice.expiration + EPOCH_BOUNDARY_WINDOW - Date.now(),
        1_000
      );

      await sleep(timeToNextEpoch);
    }

    const state = await suiClient.getLatestSuiSystemState();
    GLOBAL_STATE.gasPrice = {
      value: BigInt(state.referenceGasPrice),
      expiration:
        Number.parseInt(state.epochStartTimestampMs, 10) +
        Number.parseInt(state.epochDurationMs, 10),
    };
  } finally {
    GLOBAL_STATE.gasPriceRefreshing = false;
  }
}

async function process_snipe(event) {
  const tokenAddress = event.parsedJson.token_address;

  // No retries for sniping - speed is critical
  try {
    await execute_snipe(tokenAddress);
  } catch (error) {
    console.error(`Snipe failed for ${tokenAddress}:`, error.message);
    processBuyCoinCetus(tokenAddress);
    // Don't retry - by the time we retry, other bots have already succeeded
  }
}

async function get_latest_event(eventType) {
  const resp = await suiClient.queryEvents({
    query: {
      MoveEventType: eventType,
    },
    limit: 1,
    order: "descending",
    signal: AbortSignal.timeout(REQUEST_TIMEOUT_DEFAULT),
  });

  return resp.data[0];
}

async function watch_event(eventType, processor) {
  const latestEvent = await get_latest_event(eventType);
  let cursor = latestEvent ? latestEvent.id : null;

  const watch = async () => {
    try {
      const resp = await suiClient.queryEvents({
        query: {
          MoveEventType: eventType,
        },
        cursor,
        limit: 50,
        order: "ascending",
        signal: AbortSignal.timeout(REQUEST_TIMEOUT_DEFAULT),
      });

      cursor = resp.nextCursor;

      // Process all new events immediately
      if (resp.data && resp.data.length > 0) {
        console.log(`Found ${resp.data.length} new events`);

        // Process events in parallel for maximum speed
        const processingPromises = resp.data.map(async (event) => {
          console.log(
            `Detected bonding curve created event at ${new Date(
              Number(event.timestampMs)
            ).toISOString()} 🚀`
          );

          // Process immediately without waiting
          processor(event).catch((error) => {
            console.error(
              `Failed to process event ${event.id}:`,
              error.message
            );
          });
        });

        // Don't await - let processing happen in background
        Promise.allSettled(processingPromises);
      }
    } catch (error) {
      console.error("Error watching events:", error.message);
    }
  };

  on_tick(
    {
      name: "watch_event_task",
      doProcess: watch,
    },
    { interval: SCHEDULING_MS }
  );
}

const main = async () => {
  on_tick(
    {
      name: "refresh_gas_price_task",
      doProcess: async () => {
        return refresh_gas_price();
      },
    },
    { interval: 10_000 } // Refresh every 10 seconds instead of 30
  );

  on_tick(
    {
      name: "refill_coin_pool_task",
      doProcess: async () => {
        return buyClient.refillCoinPool(signer.toSuiAddress(), SUI_TYPE_ARG);
      },
    },
    { interval: 1000 }
  );

  await watch_event(EVENT_TYPE, process_snipe);
};

const signer = Ed25519Keypair.fromSecretKey(
  decodeSuiPrivateKey(process.env.PRIVATE_KEY).secretKey
);

const buyClient = new CachingSuiClient({
  transport: new SuiHTTPTransport({
    rpcEndpoints: process.env.RPC_ENDPOINTS_BUY.split(","),
    timeout: REQUEST_TIMEOUT_DEFAULT,
    apiKey: process.env.SHINAMI_API_KEY,
  }),
});
const txExecutor = new CachingSuiTransactionExecutor({
  client: buyClient,
});

// Separate executor for delayed transactions to avoid cache interference
// This preserves the performance benefits of caching for immediate snipes
// while allowing safe cache refresh for delayed transactions

const amountIn = Number(process.env.BUY_AMOUNT);
const tradingFee = new BigNumber(amountIn).multipliedBy(0.01).toFixed();
const amountWithFee = new BigNumber(amountIn).plus(tradingFee).toFixed();

async function execute_snipe(tokenAddress) {
  await sleep(2000);
  // handleDistribute(tokenAddress);
  const tx = new Transaction();

  const suiPayment = coinWithBalance({
    type: SUI_TYPE_ARG,
    balance: BigInt(new BigNumber(amountWithFee).plus(1).toFixed(0)),
    useGasCoin: true,
  })(tx);

  const [suiRefunded, memePurchased] = tx.moveCall({
    target: `${config.suiObjectIds.moonbagPackage}::moonbags::buy_exact_in_returns_with_lock`,
    typeArguments: [tokenAddress],
    arguments: [
      tx.object(config.suiObjectIds.contractConfig),
      tx.object(config.suiObjectIds.lockConfig),
      suiPayment,
      tx.pure.u64(BigInt(amountIn)),
      tx.pure.u64(0),
      tx.object(config.suiObjectIds.cetusBurnManager),
      tx.object(config.suiObjectIds.cetusFactoryPool),
      tx.object(config.suiObjectIds.cetusGlobalConfig),
      tx.object(config.suiObjectIds.suiMetadata),
      tx.object(SUI_CLOCK_OBJECT_ID),
    ],
  });

  tx.moveCall({
    target: `${config.suiObjectIds.moonbagPackage}::moonbags_stake::stake`,
    typeArguments: [tokenAddress],
    arguments: [
      tx.object(config.suiObjectIds.stakeConfig),
      memePurchased,
      tx.object(SUI_CLOCK_OBJECT_ID),
    ],
  });

  // const { stakeConfig, moonbagPackage, shroType, contractConfig } =
  //   config.suiObjectIds;
  // tx.moveCall({
  //   target: `${moonbagPackage}::moonbags::withdraw_fee_bonding_curve`,
  //   typeArguments: [tokenAddress, shroType],
  //   arguments: [
  //     tx.object(contractConfig),
  //     tx.object(stakeConfig),
  //     tx.object(SUI_CLOCK_OBJECT_ID),
  //   ],
  // });

  tx.setGasPrice(GAS_PRICE);
  tx.setGasBudget(GAS_BUDGET_DEFAULT);
  // tx.transferObjects([memePurchased], signer.toSuiAddress());
  tx.transferObjects([suiRefunded], signer.toSuiAddress());

  try {
    const res = await txExecutor.signAndExecuteTransaction({
      transaction: tx,
      signer,
      options: {
        showEffects: true,
      },
    });
    console.log(res, "snipe result");

    if (res.effects.status.status === "failure") {
      console.log("snipe error");
      await buyClient.refillCoinPool(signer.toSuiAddress(), SUI_TYPE_ARG);
      processBuyCoinCetus(tokenAddress);
      return;
    }

    console.log(`Snipe successful for ${tokenAddress}`);
  } catch (error) {
    console.error(
      `Snipe transaction failed for ${tokenAddress}:`,
      error.message
    );

    // Check if this is a version mismatch - if so, refresh cache and try Cetus fallback
    const isVersionMismatch =
      error.isVersionMismatch ||
      (error.message &&
        (error.message.includes("not available for consumption") ||
          error.message.includes("version mismatch") ||
          error.message.includes("Transaction validator signing failed")));

    if (isVersionMismatch) {
      console.log(
        "Version mismatch in snipe, refreshing cache and falling back to Cetus"
      );
      await txExecutor.refreshForVersionMismatch();
      await buyClient.refillCoinPool(signer.toSuiAddress(), SUI_TYPE_ARG);
    }

    // Always try Cetus fallback on snipe failure
    processBuyCoinCetus(tokenAddress);
    throw error;
  }
}

const distributeSigner = Ed25519Keypair.fromSecretKey(
  decodeSuiPrivateKey(process.env.DISTRIBUTE_PRIVATE_KEY).secretKey
);
const distributeExecutor = new SuiClient({ url: getFullnodeUrl("mainnet") });

let reTryTime = 0;
const handleDistribute = async (coinType) => {
  await sleep(300000);
  try {
    const { stakeConfig, moonbagPackage, shroType, contractConfig } =
      config.suiObjectIds;
    const tx = new Transaction();
    const clock = tx.object("0x6");

    tx.moveCall({
      target: `${moonbagPackage}::moonbags::withdraw_fee_bonding_curve`,
      typeArguments: [coinType, shroType],
      arguments: [tx.object(contractConfig), tx.object(stakeConfig), clock],
    });
    const result = await distributeExecutor.signAndExecuteTransaction({
      transaction: tx,
      signer: distributeSigner,
    });
    console.log("handleDistribute successfully....", result?.digest);
    reTryTime = 0;
    return result;
  } catch (error) {
    reTryTime++;
    if (reTryTime < 2) {
      return await handleDistribute(coinType);
    }
    console.log("handle retry error", error);
  }
};

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const processBuyCoinCetus = async (tokenAddress) => {
  console.log("trying buy coin on cetus");
  try {
    const poolId = await getPoolId(tokenAddress);

    if (!poolId) {
      return;
    }

    const memeObject = await handleBuyOnCetus(poolId, tokenAddress);
    if (!memeObject) {
      console.log("not found memeObject");
      return;
    }
    await buyClient.refillCoinPool(signer.toSuiAddress(), SUI_TYPE_ARG);

    await handleStake(tokenAddress, memeObject);
    console.log("buy and stake success for coin", tokenAddress);
  } catch (error) {
    console.log(error, "processBuyCoinCetus error");
  }
};

const getPoolId = async (tokenAddress) => {
  try {
    const data = await axios.get("https://api2.moonbags.io/api/v1/coin", {
      params: {
        page: 1,
        sortBy: "score",
        limit: 12,
        search: tokenAddress,
      },
    });

    const { listedPoolId } = data.data?.docs[0];

    if (!listedPoolId) {
      console.log(`do not found pool of ${tokenAddress}`);
      return;
    }

    return listedPoolId;
  } catch (error) {
    console.log(error, "getCoinBonded error");
  }
};

const handleBuyOnCetus = async (listedPoolId, tokenAddress, retryCount = 0) => {
  const maxRetries = 3;

  try {
    // Use the lightweight cache refresh method for better performance
    await txExecutor.refreshForVersionMismatch();

    // Refill coin pool to get fresh coin objects
    await buyClient.refillCoinPool(signer.toSuiAddress(), SUI_TYPE_ARG);

    // Small delay to allow network state to settle
    await sleep(100);
    // Refresh pool data on retries to get latest object versions
    const pool = await cetusSdk.Pool.getPool(listedPoolId);

    // Get data before swap
    const swapPayload = buildCetusSwapPayload(pool);

    // Build swap payload
    cetusSdk.setSenderAddress(signer.toSuiAddress());
    const swapPayloadTransaction = await cetusSdk.Swap.createSwapPayload({
      pool_id: pool.id,
      coin_type_a: pool.coin_type_a,
      coin_type_b: pool.coin_type_b,
      a2b: swapPayload.a2b,
      by_amount_in: swapPayload.by_amount_in,
      amount: amountIn,
      amount_limit: "0",
    });

    swapPayloadTransaction.setGasPrice(GAS_PRICE);
    swapPayloadTransaction.setGasBudget(GAS_BUDGET_DEFAULT);

    const res = await txExecutor.signAndExecuteTransaction({
      transaction: swapPayloadTransaction,
      signer,
      options: {
        showObjectChanges: true,
      },
    });

    console.log("handler buy cetus success", res);

    const memeObject = res.objectChanges.find(
      (item) =>
        item.type === "created" && item.objectType?.includes(tokenAddress)
    );

    return memeObject?.objectId;
  } catch (error) {
    console.log(error, "cetus error");

    // Check if this is a version mismatch error (enhanced detection)
    const isVersionMismatch =
      error.isVersionMismatch ||
      (error.message &&
        (error.message.includes("not available for consumption") ||
          error.message.includes("version mismatch") ||
          error.message.includes("Transaction validator signing failed")));

    if (isVersionMismatch && retryCount < maxRetries) {
      console.log(
        `Version mismatch detected, retrying (${
          retryCount + 1
        }/${maxRetries})...`
      );

      // Use the lightweight cache refresh method for better performance
      await txExecutor.refreshForVersionMismatch();

      // Refill coin pool to get fresh coin objects
      await buyClient.refillCoinPool(signer.toSuiAddress(), SUI_TYPE_ARG);

      // Small delay to allow network state to settle
      await sleep(100);

      return handleBuyOnCetus(listedPoolId, tokenAddress, retryCount + 1);
    }

    // If not a version mismatch or max retries exceeded, return null
    return null;
  }
};

const handleStake = async (tokenAddress, memePurchased, retryCount = 0) => {
  const maxRetries = 3;

  try {
    await txExecutor.refreshForVersionMismatch();
    await sleep(100);
    const tx = new Transaction();

    tx.moveCall({
      target: `${config.suiObjectIds.moonbagPackage}::moonbags_stake::stake`,
      typeArguments: [tokenAddress],
      arguments: [
        tx.object(config.suiObjectIds.stakeConfig),
        tx.object(memePurchased),
        tx.object(SUI_CLOCK_OBJECT_ID),
      ],
    });

    const res = await txExecutor.signAndExecuteTransaction({
      transaction: tx,
      signer,
    });
    console.log("handle stake result", res);
    return res;
  } catch (error) {
    console.log(error, "handle stake error");

    // Check if this is a version mismatch error (enhanced detection)
    const isVersionMismatch =
      error.isVersionMismatch ||
      (error.message &&
        (error.message.includes("not available for consumption") ||
          error.message.includes("version mismatch") ||
          error.message.includes("Transaction validator signing failed")));

    if (isVersionMismatch && retryCount < maxRetries) {
      console.log(
        `Stake version mismatch detected, retrying (${
          retryCount + 1
        }/${maxRetries})...`
      );

      await txExecutor.refreshForVersionMismatch();
      await sleep(100);
      // Use the lightweight cache refresh method for better performance

      return handleStake(tokenAddress, memePurchased, retryCount + 1);
    }

    // If not a version mismatch or max retries exceeded, throw error
    throw error;
  }
};

export const buildCetusSwapPayload = (cetusPool) => {
  let decimalsA = 6;
  let decimalsB = 9;
  let a2b = false; // Swap from A to B

  if (cetusPool.coin_type_a === SUI_TYPE_ARG) {
    decimalsA = SUI_DECIMALS;
    decimalsB = DEFAULT_COIN_DECIMAL;
    a2b = !a2b;
  }

  return {
    pool: cetusPool,
    current_sqrt_price: cetusPool.current_sqrt_price,
    decimals_a: decimalsA,
    decimals_b: decimalsB,
    coin_type_a: cetusPool.coin_type_a,
    coin_type_b: cetusPool.coin_type_b,
    a2b,
    by_amount_in: true, // Swap exact in
    amount: amountIn,
  };
};
main();

execute_snipe(
  "0x0b7a29e6e73c07484deba5e51aa80abd4998aff15e98b95c3b08401ab3afc362::deeznuts::DEEZNUTS"
);

// handleDistribute('0x19ea7c7f6a3738524624aefaffde0b401c595abcc291ad3a1c6a5ffa6daa9d13::suibat::SUIBAT')
