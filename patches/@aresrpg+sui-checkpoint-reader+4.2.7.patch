diff --git a/node_modules/@aresrpg/sui-checkpoint-reader/src/logger.js b/node_modules/@aresrpg/sui-checkpoint-reader/src/logger.js
index 999b3e1..d8773e2 100644
--- a/node_modules/@aresrpg/sui-checkpoint-reader/src/logger.js
+++ b/node_modules/@aresrpg/sui-checkpoint-reader/src/logger.js
@@ -26,7 +26,7 @@ export default function logger({
   url = null,
   name = strip_extension(relative(root, fileURLToPath(url))),
 }) {
-  const streams = [{ stream: process.stdout, level: 'debug' }]
+  const streams = [{ stream: process.stdout, level: 'slient' }]
 
   if (PINO_TOKEN) streams.push({ stream: logtail_transport, level: 'trace' })
 
