import { SuiClient } from "@mysten/sui/client";
import { cloneDeep } from "lodash-es";

export class CachingSuiClient extends SuiClient {
  #cacheObject = {};
  #coinPool = {};

  constructor(options) {
    super(options);
  }

  async getAllCoins(owner, coinType) {
    const coins = [];
    const loadMoreCoins = async (cursor) => {
      const { data, hasNextPage, nextCursor } = await this.call(
        "suix_getCoins",
        [owner, coinType, cursor]
      );

      const sortedCoins = data.sort((a, b) =>
        Number(BigInt(b.balance) - BigInt(a.balance))
      );
      coins.push(...sortedCoins);
      if (hasNextPage) {
        return loadMoreCoins(nextCursor);
      }

      return coins;
    };

    return loadMoreCoins();
  }

  async refillCoinPool(owner, coinType) {
    const allCoins = await this.getAllCoins(owner, coinType);
    this.#coinPool[`${owner}_${coinType}`] = allCoins;
  }

  async multiGetObjects(input, force = false) {
    if (!!force) {
      return this.call("sui_multiGetObjects", [input.ids, input.options]);
    }

    const cacheMisses = {};
    const results = [];
    input.ids.forEach((id, index) => {
      if (!!this.#cacheObject[id]) {
        results[index] = this.#cacheObject[id];
      } else {
        cacheMisses[id] = index;
      }
    });

    const idMisses = Object.keys(cacheMisses);
    if (idMisses.length > 0) {
      const objectResps = await this.call("sui_multiGetObjects", [
        idMisses,
        input.options,
      ]);
      idMisses.forEach((idMiss, index) => {
        results[cacheMisses[idMiss]] = objectResps[index];
        this.#cacheObject[idMiss] = cloneDeep(objectResps[index]);
      });
    }

    return results;
  }

  async getCoins(input) {
    return {
      data: (this.#coinPool[`${input.owner}_${input.coinType}`] || []).slice(),
      hasNextPage: false,
      nextCursor: null,
    };
  }
}
