import { bcs } from "@mysten/sui/bcs";
import {
  isTransaction,
  ObjectCache,
  Transaction,
} from "@mysten/sui/transactions";

export class CachingSuiTransactionExecutor {
  constructor({ client, ...options }) {
    this.client = client;
    this.lastDigest = null;
    this.cache = new ObjectCache(options);
  }

  /**
   * Clears all Owned objects
   * Immutable objects, Shared objects, and Move function definitions will be preserved
   */
  async reset() {
    await Promise.all([
      this.cache.clearOwnedObjects(),
      this.cache.clearCustom(),
      this.waitForLastTransaction(),
    ]);
  }

  async buildTransaction({ transaction, ...options }) {
    transaction.addBuildPlugin(this.cache.asPlugin());
    return transaction.build({
      client: this.client,
      ...options,
    });
  }

  async dryTransaction({ transaction, ...options }) {
    const simulationResult = await this.client.dryRunTransactionBlock({
      transactionBlock: await transaction.build({
        client: this.client,
      }),
    });

    return {
      simulationResult,
      transaction,
    };
  }

  async executeTransaction({ transaction, options, ...input }) {
    const bytes = isTransaction(transaction)
      ? await this.buildTransaction({ transaction })
      : transaction;

    const results = await this.client.executeTransactionBlock({
      ...input,
      transactionBlock: bytes,
      options: {
        ...options,
        showRawEffects: true,
      },
    });

    if (results.rawEffects) {
      const effects = bcs.TransactionEffects.parse(
        Uint8Array.from(results.rawEffects)
      );
      await this.applyEffects(effects);
    }

    return results;
  }

  async signAndExecuteTransaction({ transaction, signer, options, ...input }) {
    transaction.setSenderIfNotSet(signer.toSuiAddress());
    const bytes = await this.buildTransaction({ transaction });
    const { signature } = await signer.signTransaction(bytes);
    const results = await this.executeTransaction({
      transaction: bytes,
      signature,
      options,
    });

    return results;
  }

  async applyEffects(effects) {
    this.lastDigest = effects.V2?.transactionDigest ?? null;
    await this.cache.applyEffects(effects);
  }

  async waitForLastTransaction() {
    if (this.lastDigest) {
      await this.client.waitForTransaction({ digest: this.lastDigest });
      this.lastDigest = null;
    }
  }
}
